{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/HAVAC/my-app/src/components/Navbar.jsx"], "sourcesContent": ["'use client';\r\nimport React, { useState } from 'react';\r\n\r\nconst Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  const toggleMenu = () => {\r\n    setIsMenuOpen(!isMenuOpen);\r\n  };\r\n\r\n  return (\r\n    <nav className='fixed top-0 left-0 right-0 bg-white shadow-lg z-50'>\r\n      <div className='max-w-6xl mx-auto px-4'>\r\n        <div className='flex justify-between items-center h-16'>\r\n          {/* Logo */}\r\n          <div className='flex items-center'>\r\n            <a href='#' className='text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors'>\r\n              HAVAC\r\n            </a>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <div className='hidden md:flex items-center space-x-8'>\r\n            <a href='#' className='text-gray-700 hover:text-blue-600 transition-colors font-medium'>\r\n              Home\r\n            </a>\r\n            <a href='#' className='text-gray-700 hover:text-blue-600 transition-colors font-medium'>\r\n              About\r\n            </a>\r\n            <a href='#' className='text-gray-700 hover:text-blue-600 transition-colors font-medium'>\r\n              Services\r\n            </a>\r\n            <a href='#' className='text-gray-700 hover:text-blue-600 transition-colors font-medium'>\r\n              Contact\r\n            </a>\r\n            <button className='bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors'>\r\n              Get Started\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile menu button */}\r\n          <div className='md:hidden'>\r\n            <button\r\n              onClick={toggleMenu}\r\n              className='text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600'\r\n            >\r\n              <svg className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>\r\n                {isMenuOpen ? (\r\n                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M6 18L18 6M6 6l12 12' />\r\n                ) : (\r\n                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 6h16M4 12h16M4 18h16' />\r\n                )}\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Navigation */}\r\n        {isMenuOpen && (\r\n          <div className='md:hidden'>\r\n            <div className='px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200'>\r\n              <a href='#' className='block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium'>\r\n                Home\r\n              </a>\r\n              <a href='#' className='block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium'>\r\n                About\r\n              </a>\r\n              <a href='#' className='block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium'>\r\n                Services\r\n              </a>\r\n              <a href='#' className='block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium'>\r\n                Contact\r\n              </a>\r\n              <div className='px-3 py-2'>\r\n                <button className='w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors'>\r\n                  Get Started\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAAyE;;;;;;;;;;;sCAMjG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAkE;;;;;;8CAGxF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAkE;;;;;;8CAGxF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAkE;;;;;;8CAGxF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAkE;;;;;;8CAGxF,8OAAC;oCAAO,WAAU;8CAA8F;;;;;;;;;;;;sCAMlH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC7D,2BACC,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;iGAErE,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAA4F;;;;;;0CAGlH,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAA4F;;;;;;0CAGlH,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAA4F;;;;;;0CAGlH,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAA4F;;;;;;0CAGlH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;8CAAqG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvI;uCAEe", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/HAVAC/my-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/HAVAC/my-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/HAVAC/my-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}