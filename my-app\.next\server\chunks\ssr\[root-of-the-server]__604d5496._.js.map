{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/HAVAC/my-app/src/app/page.js"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Hero Section */}\n      <section className=\"pt-20 pb-16 px-4\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <h1 className=\"text-5xl md:text-7xl font-bold text-gray-900 mb-6\">\n            Welcome to <span className=\"text-blue-600\">HAVAC</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Building the future with innovative solutions and cutting-edge technology\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors\">\n              Get Started\n            </button>\n            <button className=\"border border-gray-300 hover:border-gray-400 text-gray-700 px-8 py-3 rounded-lg font-semibold transition-colors\">\n              Learn More\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 px-4 bg-white\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12\">\n            Our Features\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Fast Performance</h3>\n              <p className=\"text-gray-600\">Lightning-fast performance with optimized code and modern architecture.</p>\n            </div>\n\n            <div className=\"text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Reliable</h3>\n              <p className=\"text-gray-600\">Built with reliability in mind, ensuring consistent performance and uptime.</p>\n            </div>\n\n            <div className=\"text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">User Friendly</h3>\n              <p className=\"text-gray-600\">Intuitive design and seamless user experience for all skill levels.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-16 px-4 bg-blue-600\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Ready to Get Started?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Join thousands of users who trust HAVAC for their needs\n          </p>\n          <button className=\"bg-white hover:bg-gray-100 text-blue-600 px-8 py-3 rounded-lg font-semibold transition-colors\">\n            Start Your Journey\n          </button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12 px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">HAVAC</h3>\n              <p className=\"text-gray-400\">Building innovative solutions for tomorrow's challenges.</p>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-4\">Product</h4>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Features</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Pricing</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Documentation</a></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-4\">Company</h4>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">About</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Blog</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Careers</a></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-4\">Support</h4>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Help Center</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Contact</a></li>\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Status</a></li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n            <p>&copy; 2024 HAVAC. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;sCAGxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAAgG;;;;;;8CAGlH,8OAAC;oCAAO,WAAU;8CAAkH;;;;;;;;;;;;;;;;;;;;;;;0BAQ1I,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAO,WAAU;sCAAgG;;;;;;;;;;;;;;;;;0BAOtH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAGnE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAGnE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAIrE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}